package com.kun.linkage.auth.facade.vo.boss;

import com.kun.linkage.common.base.page.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;

/**
 * 导出文件记录查询请求VO
 */
@Schema(description = "导出文件记录查询请求")
public class ExportFileRecordQueryVO extends PageParam implements Serializable {
    
    private static final long serialVersionUID = 1L;

    @Schema(description = "文件名", example = "Authorization_123456_20231001120000.csv")
    private String fileName;

    @Schema(description = "文件状态", example = "SUCCESS")
    private String fileStatus;

    @Schema(description = "商户号", example = "123456789")
    private String merchantNo;

    @Schema(description = "导出类型", example = "AUTHORIZATION_EXPORT")
    private String exportType;

    @Schema(description = "创建开始日期", example = "2023-10-01")
    private String createdDateFrom;

    @Schema(description = "创建结束日期", example = "2023-10-31")
    private String createdDateUntil;

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public String getFileStatus() {
        return fileStatus;
    }

    public void setFileStatus(String fileStatus) {
        this.fileStatus = fileStatus;
    }

    public String getMerchantNo() {
        return merchantNo;
    }

    public void setMerchantNo(String merchantNo) {
        this.merchantNo = merchantNo;
    }

    public String getExportType() {
        return exportType;
    }

    public void setExportType(String exportType) {
        this.exportType = exportType;
    }

    public String getCreatedDateFrom() {
        return createdDateFrom;
    }

    public void setCreatedDateFrom(String createdDateFrom) {
        this.createdDateFrom = createdDateFrom;
    }

    public String getCreatedDateUntil() {
        return createdDateUntil;
    }

    public void setCreatedDateUntil(String createdDateUntil) {
        this.createdDateUntil = createdDateUntil;
    }
}
