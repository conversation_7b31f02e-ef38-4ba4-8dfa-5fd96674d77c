package com.kun.linkage.auth.facade.vo.boss;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 导出文件记录响应VO
 */
@Schema(description = "导出文件记录")
public class ExportFileRecordVO implements Serializable {
    
    private static final long serialVersionUID = 1L;

    @Schema(description = "文件记录ID", example = "1234567890")
    private String fileRecordId;

    @Schema(description = "文件名", example = "Authorization_123456_20231001120000.csv")
    private String fileName;

    @Schema(description = "文件类型", example = "CSV")
    private String fileType;

    @Schema(description = "文件状态", example = "SUCCESS")
    private String fileStatus;

    @Schema(description = "S3文件URL", example = "https://s3.amazonaws.com/bucket/file.csv")
    private String s3Url;

    @Schema(description = "文件大小（字节）", example = "1024000")
    private Long fileSize;

    @Schema(description = "商户号", example = "123456789")
    private String merchantNo;

    @Schema(description = "创建用户ID", example = "user123")
    private String createdBy;

    @Schema(description = "创建时间", example = "2023-10-01T12:00:00")
    private LocalDateTime createdTime;

    @Schema(description = "更新时间", example = "2023-10-01T12:05:00")
    private LocalDateTime updatedTime;

    @Schema(description = "失败原因", example = "网络超时")
    private String errorMessage;

    @Schema(description = "导出类型", example = "AUTHORIZATION_EXPORT")
    private String exportType;

    public String getFileRecordId() {
        return fileRecordId;
    }

    public void setFileRecordId(String fileRecordId) {
        this.fileRecordId = fileRecordId;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public String getFileType() {
        return fileType;
    }

    public void setFileType(String fileType) {
        this.fileType = fileType;
    }

    public String getFileStatus() {
        return fileStatus;
    }

    public void setFileStatus(String fileStatus) {
        this.fileStatus = fileStatus;
    }

    public String getS3Url() {
        return s3Url;
    }

    public void setS3Url(String s3Url) {
        this.s3Url = s3Url;
    }

    public Long getFileSize() {
        return fileSize;
    }

    public void setFileSize(Long fileSize) {
        this.fileSize = fileSize;
    }

    public String getMerchantNo() {
        return merchantNo;
    }

    public void setMerchantNo(String merchantNo) {
        this.merchantNo = merchantNo;
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    public LocalDateTime getCreatedTime() {
        return createdTime;
    }

    public void setCreatedTime(LocalDateTime createdTime) {
        this.createdTime = createdTime;
    }

    public LocalDateTime getUpdatedTime() {
        return updatedTime;
    }

    public void setUpdatedTime(LocalDateTime updatedTime) {
        this.updatedTime = updatedTime;
    }

    public String getErrorMessage() {
        return errorMessage;
    }

    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }

    public String getExportType() {
        return exportType;
    }

    public void setExportType(String exportType) {
        this.exportType = exportType;
    }
}
