package com.kun.linkage.auth.facade.vo.boss;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 授权记录异步导出请求VO
 */
@Schema(description = "授权记录异步导出请求")
public class AuthorizationExportRequestVO implements Serializable {
    
    private static final long serialVersionUID = 1L;

    @Schema(description = "授权日期 - 开始日期", example = "2023-10-01", required = true)
    private String authorizationDateFrom;

    @Schema(description = "授权日期 - 结束日期", example = "2023-10-01", required = true)
    private String authorizationDateUntil;

    @Schema(description = "交易金额 - 开始", example = "100.00")
    private BigDecimal transAmountFrom;

    @Schema(description = "交易金额 - 结束", example = "100.00")
    private BigDecimal transAmountTo;

    @Schema(description = "系统商户号/机构号", example = "123456789")
    private String merchantNo;

    @Schema(description = "收单机构商户号", example = "987654321")
    private String acquirerMerchantNo;

    @Schema(description = "授权交易ID", example = "AUTH1234567890")
    private String authTransId;

    @Schema(description = "授权交易类型,字典:KL_AUTH_TRANS_TYPE", example = "010000")
    private String authTransType;

    @Schema(description = "原授权交易ID", example = "AUTH1234567889")
    private String originalAuthTransId;

    @Schema(description = "收单机构商户MCC", example = "5411")
    private String acquirerMerchantMcc;

    @Schema(description = "收单参考号", example = "REF1234567890")
    private String acquireReferenceNo;

    @Schema(description = "网关卡ID", example = "K-1234567890")
    private String gatewayCardId;

    @Schema(description = "授权状态", example = "SUCCESS")
    private String authStatus;

    @Schema(description = "响应码", example = "0000")
    private String responseCode;

    @Schema(description = "系统跟踪审计号", example = "005354")
    private String systemsTraceAuditNumber;

    @Schema(description = "授权码", example = "NVZAT1")
    private String approveCode;

    @Schema(description = "processor,字典:KL_PROCESSOR", example = "BPC-GW")
    private String processor;

    @Schema(description = "卡号后四位", example = "1234")
    private String cardNoSuffix;

    public String getAuthorizationDateFrom() {
        return authorizationDateFrom;
    }

    public void setAuthorizationDateFrom(String authorizationDateFrom) {
        this.authorizationDateFrom = authorizationDateFrom;
    }

    public String getAuthorizationDateUntil() {
        return authorizationDateUntil;
    }

    public void setAuthorizationDateUntil(String authorizationDateUntil) {
        this.authorizationDateUntil = authorizationDateUntil;
    }

    public BigDecimal getTransAmountFrom() {
        return transAmountFrom;
    }

    public void setTransAmountFrom(BigDecimal transAmountFrom) {
        this.transAmountFrom = transAmountFrom;
    }

    public BigDecimal getTransAmountTo() {
        return transAmountTo;
    }

    public void setTransAmountTo(BigDecimal transAmountTo) {
        this.transAmountTo = transAmountTo;
    }

    public String getMerchantNo() {
        return merchantNo;
    }

    public void setMerchantNo(String merchantNo) {
        this.merchantNo = merchantNo;
    }

    public String getAcquirerMerchantNo() {
        return acquirerMerchantNo;
    }

    public void setAcquirerMerchantNo(String acquirerMerchantNo) {
        this.acquirerMerchantNo = acquirerMerchantNo;
    }

    public String getAuthTransId() {
        return authTransId;
    }

    public void setAuthTransId(String authTransId) {
        this.authTransId = authTransId;
    }

    public String getAuthTransType() {
        return authTransType;
    }

    public void setAuthTransType(String authTransType) {
        this.authTransType = authTransType;
    }

    public String getOriginalAuthTransId() {
        return originalAuthTransId;
    }

    public void setOriginalAuthTransId(String originalAuthTransId) {
        this.originalAuthTransId = originalAuthTransId;
    }

    public String getAcquirerMerchantMcc() {
        return acquirerMerchantMcc;
    }

    public void setAcquirerMerchantMcc(String acquirerMerchantMcc) {
        this.acquirerMerchantMcc = acquirerMerchantMcc;
    }

    public String getAcquireReferenceNo() {
        return acquireReferenceNo;
    }

    public void setAcquireReferenceNo(String acquireReferenceNo) {
        this.acquireReferenceNo = acquireReferenceNo;
    }

    public String getGatewayCardId() {
        return gatewayCardId;
    }

    public void setGatewayCardId(String gatewayCardId) {
        this.gatewayCardId = gatewayCardId;
    }

    public String getAuthStatus() {
        return authStatus;
    }

    public void setAuthStatus(String authStatus) {
        this.authStatus = authStatus;
    }

    public String getResponseCode() {
        return responseCode;
    }

    public void setResponseCode(String responseCode) {
        this.responseCode = responseCode;
    }

    public String getSystemsTraceAuditNumber() {
        return systemsTraceAuditNumber;
    }

    public void setSystemsTraceAuditNumber(String systemsTraceAuditNumber) {
        this.systemsTraceAuditNumber = systemsTraceAuditNumber;
    }

    public String getApproveCode() {
        return approveCode;
    }

    public void setApproveCode(String approveCode) {
        this.approveCode = approveCode;
    }

    public String getProcessor() {
        return processor;
    }

    public void setProcessor(String processor) {
        this.processor = processor;
    }

    public String getCardNoSuffix() {
        return cardNoSuffix;
    }

    public void setCardNoSuffix(String cardNoSuffix) {
        this.cardNoSuffix = cardNoSuffix;
    }
}
