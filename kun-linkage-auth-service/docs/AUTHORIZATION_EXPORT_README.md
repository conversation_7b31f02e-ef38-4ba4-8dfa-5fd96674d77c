# 授权记录异步导出功能

## 功能概述

为OrgAuthorizationQueryController新增了授权记录数据异步导出功能，支持将授权记录导出为CSV格式文件并上传到S3存储。

## 主要特性

1. **异步导出处理** - 使用异步机制处理大数据量导出，避免接口超时
2. **日期范围限制** - 授权日期范围最大31天，确保查询性能
3. **CSV格式导出** - 标准CSV格式，包含完整的授权记录信息
4. **S3文件存储** - 自动上传到S3并获取下载链接
5. **文件记录管理** - 完整的文件记录查询和下载功能
6. **错误处理** - 完善的异常处理和状态跟踪

## 新增接口

### 1. 异步导出授权记录
```
POST /org/authorization/asyncExport
```

**请求参数：**
```json
{
  "authorizationDateFrom": "2023-10-01",
  "authorizationDateUntil": "2023-10-31",
  "merchantNo": "123456789",
  "authTransType": "010000",
  "authStatus": "SUCCESS",
  // ... 其他查询条件
}
```

**响应：**
```json
{
  "code": "0000",
  "message": "Success",
  "data": "1234567890" // 文件记录ID
}
```

### 2. 查询导出文件记录
```
POST /org/authorization/exportFiles/pageList
```

**请求参数：**
```json
{
  "pageNum": 1,
  "pageSize": 10,
  "fileName": "Authorization_",
  "fileStatus": "SUCCESS",
  "merchantNo": "123456789"
}
```

### 3. 获取文件下载URL
```
POST /org/authorization/exportFiles/downloadUrl
```

**请求参数：**
```json
"1234567890" // 文件记录ID
```

## CSV文件格式

导出的CSV文件包含以下列（按顺序）：

1. 授权日期时间
2. 系统授权号
3. 商户号
4. Card ID
5. 授权类型
6. 交易币种
7. 交易金额
8. 持卡人币种
9. 持卡人金额
10. 授权状态
11. 参考号
12. 授权码
13. 原系统授权号
14. 收单商户名称
15. 收单商户MCC
16. 清算状态

## 文件命名规则

```
Authorization_{Merchant No}_YYYMMDDhhmmss.csv
```

例如：`Authorization_123456_20231001120000.csv`

## 数据库表

### kl_export_file_record

通用导出文件记录表，用于跟踪所有导出任务的状态。

主要字段：
- `file_record_id` - 文件记录ID（主键）
- `file_name` - 文件名
- `file_status` - 文件状态（PROCESSING/SUCCESS/FAILED）
- `s3_url` - S3文件URL
- `merchant_no` - 商户号
- `export_type` - 导出类型

## 技术实现

### 1. 异步处理
使用Spring的@Async注解和现有的线程池`externalApiAsyncExecutor`进行异步处理。

### 2. CSV生成
使用Apache Commons CSV库生成标准格式的CSV文件。

### 3. S3上传
复用现有的AwsS3Util工具类进行文件上传。

### 4. 状态管理
通过数据库记录跟踪导出任务的状态变化。

## 配置要求

### 1. 依赖
已添加Apache Commons CSV依赖：
```xml
<dependency>
    <groupId>org.apache.commons</groupId>
    <artifactId>commons-csv</artifactId>
    <version>1.9.0</version>
</dependency>
```

### 2. 数据库
需要执行SQL脚本创建`kl_export_file_record`表。

### 3. S3配置
确保S3相关配置正确，包括：
- AWS访问密钥
- S3桶名称
- 文件夹路径配置

## 使用流程

1. **发起导出请求** - 调用异步导出接口，获取文件记录ID
2. **查询导出状态** - 通过文件记录查询接口检查导出进度
3. **下载文件** - 导出成功后，通过下载URL接口获取文件链接

## 注意事项

1. **日期范围限制** - 授权日期范围不能超过31天
2. **大数据量处理** - 对于大量数据，导出可能需要较长时间
3. **文件有效期** - S3文件的有效期根据S3配置决定
4. **权限控制** - 确保用户有相应的导出权限
5. **错误处理** - 导出失败时会记录错误信息，便于排查问题

## 扩展性

该导出框架设计为通用框架，可以轻松扩展支持其他类型的数据导出：

1. 新增导出类型枚举
2. 实现对应的数据查询逻辑
3. 定义CSV格式和文件命名规则
4. 复用现有的异步处理和文件管理功能
