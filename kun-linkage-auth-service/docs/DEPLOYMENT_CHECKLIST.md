# 授权记录异步导出功能部署检查清单

## 1. 数据库变更

### 1.1 创建表
- [ ] 执行 `docs/sql/kl_export_file_record.sql` 创建导出文件记录表
- [ ] 验证表结构和索引是否正确创建

### 1.2 权限检查
- [ ] 确认应用数据库用户对新表有读写权限

## 2. 代码部署

### 2.1 新增文件检查
- [ ] `ExportFileStatusEnum.java` - 导出文件状态枚举
- [ ] `ExportConstant.java` - 导出相关常量
- [ ] `ExportFileRecord.java` - 导出文件记录实体
- [ ] `ExportFileRecordMapper.java` - 数据访问接口
- [ ] `AuthorizationExportRequestVO.java` - 导出请求VO
- [ ] `ExportFileRecordQueryVO.java` - 文件记录查询VO
- [ ] `ExportFileRecordVO.java` - 文件记录响应VO
- [ ] `AuthorizationExportService.java` - 导出服务
- [ ] `ExportFileRecordService.java` - 文件记录服务

### 2.2 修改文件检查
- [ ] `KunLinkageAuthResponseCodeConstant.java` - 新增响应码
- [ ] `OrgAuthorizationQueryController.java` - 新增导出接口
- [ ] `pom.xml` - 新增CSV依赖

## 3. 配置检查

### 3.1 S3配置
- [ ] 确认S3访问密钥配置正确
- [ ] 确认S3桶名称和权限配置
- [ ] 确认文件夹路径配置 `kun.aws.s3.fileFolder`

### 3.2 异步任务配置
- [ ] 确认线程池配置合理
- [ ] 确认异步任务超时配置

## 4. 功能测试

### 4.1 基本功能测试
- [ ] 测试异步导出接口 `/org/authorization/asyncExport`
- [ ] 测试文件记录查询接口 `/org/authorization/exportFiles/pageList`
- [ ] 测试文件下载URL接口 `/org/authorization/exportFiles/downloadUrl`

### 4.2 边界条件测试
- [ ] 测试日期范围超过31天的情况
- [ ] 测试必填参数缺失的情况
- [ ] 测试大数据量导出的性能

### 4.3 异常情况测试
- [ ] 测试S3上传失败的处理
- [ ] 测试数据库连接异常的处理
- [ ] 测试文件生成失败的处理

## 5. 性能测试

### 5.1 导出性能
- [ ] 测试不同数据量的导出时间
- [ ] 测试并发导出的性能影响
- [ ] 测试内存使用情况

### 5.2 系统影响
- [ ] 测试导出对查询接口的影响
- [ ] 测试线程池资源使用情况

## 6. 监控和日志

### 6.1 日志配置
- [ ] 确认导出相关日志级别配置
- [ ] 确认错误日志能正确记录

### 6.2 监控指标
- [ ] 设置导出任务成功率监控
- [ ] 设置导出任务耗时监控
- [ ] 设置S3上传成功率监控

## 7. 文档和培训

### 7.1 技术文档
- [ ] 确认API文档更新
- [ ] 确认数据库文档更新

### 7.2 用户培训
- [ ] 准备用户使用手册
- [ ] 进行用户培训

## 8. 回滚计划

### 8.1 代码回滚
- [ ] 准备代码回滚方案
- [ ] 确认回滚后的兼容性

### 8.2 数据库回滚
- [ ] 准备表删除脚本（如需要）
- [ ] 确认数据备份策略

## 9. 上线后验证

### 9.1 功能验证
- [ ] 验证所有接口正常工作
- [ ] 验证文件能正确生成和下载

### 9.2 性能验证
- [ ] 监控系统性能指标
- [ ] 监控错误率和响应时间

## 10. 问题处理

### 10.1 常见问题
- [ ] 准备常见问题解决方案
- [ ] 准备故障排查手册

### 10.2 联系方式
- [ ] 确认技术支持联系方式
- [ ] 确认紧急联系流程
