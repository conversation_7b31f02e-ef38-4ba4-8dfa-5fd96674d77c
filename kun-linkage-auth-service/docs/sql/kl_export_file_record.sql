-- 通用导出文件记录表
CREATE TABLE `kl_export_file_record` (
  `file_record_id` varchar(64) NOT NULL COMMENT '文件记录ID',
  `file_name` varchar(255) NOT NULL COMMENT '文件名',
  `file_type` varchar(10) NOT NULL COMMENT '文件类型',
  `file_status` varchar(20) NOT NULL COMMENT '文件状态：PROCESSING-处理中，SUCCESS-成功，FAILED-失败',
  `s3_url` varchar(500) DEFAULT NULL COMMENT 'S3文件URL',
  `file_size` bigint(20) DEFAULT NULL COMMENT '文件大小（字节）',
  `merchant_no` varchar(50) DEFAULT NULL COMMENT '商户号',
  `created_by` varchar(64) DEFAULT NULL COMMENT '创建用户ID',
  `created_time` datetime NOT NULL COMMENT '创建时间',
  `updated_time` datetime NOT NULL COMMENT '更新时间',
  `error_message` text COMMENT '失败原因',
  `export_type` varchar(50) DEFAULT NULL COMMENT '导出类型（如：AUTHORIZATION_EXPORT）',
  PRIMARY KEY (`file_record_id`),
  KEY `idx_merchant_no` (`merchant_no`),
  KEY `idx_file_status` (`file_status`),
  KEY `idx_export_type` (`export_type`),
  KEY `idx_created_time` (`created_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='通用导出文件记录表';
