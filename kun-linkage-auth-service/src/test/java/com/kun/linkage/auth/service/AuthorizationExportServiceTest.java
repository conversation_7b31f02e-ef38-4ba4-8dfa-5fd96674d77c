package com.kun.linkage.auth.service;

import com.kun.linkage.auth.facade.vo.boss.AuthorizationExportRequestVO;
import com.kun.linkage.common.base.Result;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import javax.annotation.Resource;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 授权记录导出服务测试
 */
@SpringBootTest
@ActiveProfiles("test")
public class AuthorizationExportServiceTest {

    @Resource
    private AuthorizationExportService authorizationExportService;

    @Test
    public void testCreateExportTask() {
        // 准备测试数据
        AuthorizationExportRequestVO requestVO = new AuthorizationExportRequestVO();
        requestVO.setAuthorizationDateFrom("2023-10-01");
        requestVO.setAuthorizationDateUntil("2023-10-31");
        requestVO.setMerchantNo("123456789");

        // 执行测试
        Result<String> result = authorizationExportService.createExportTask(requestVO, "testUser");

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertNotNull(result.getData());
    }

    @Test
    public void testCreateExportTaskWithInvalidDateRange() {
        // 准备测试数据 - 超过31天的日期范围
        AuthorizationExportRequestVO requestVO = new AuthorizationExportRequestVO();
        requestVO.setAuthorizationDateFrom("2023-10-01");
        requestVO.setAuthorizationDateUntil("2023-12-01"); // 超过31天
        requestVO.setMerchantNo("123456789");

        // 执行测试
        Result<String> result = authorizationExportService.createExportTask(requestVO, "testUser");

        // 验证结果
        assertNotNull(result);
        assertFalse(result.isSuccess());
        assertEquals("KLAU1021", result.getCode()); // DATE_RANGE_EXCEEDED
    }
}
