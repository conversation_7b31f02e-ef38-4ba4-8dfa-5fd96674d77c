package com.kun.linkage.auth.controller.org;

import com.kun.linkage.auth.facade.constant.AuthApplicationRequestParamNameConstant;
import com.kun.linkage.auth.facade.constant.KunLinkageAuthResponseCodeConstant;
import com.kun.linkage.auth.facade.vo.boss.AuthorizationInquiryPageVO;
import com.kun.linkage.auth.facade.vo.boss.AuthorizationInquiryReuqestVO;
import com.kun.linkage.auth.facade.vo.boss.AuthorizationExportRequestVO;
import com.kun.linkage.auth.facade.vo.boss.ExportFileRecordQueryVO;
import com.kun.linkage.auth.facade.vo.boss.ExportFileRecordVO;
import com.kun.linkage.auth.service.AuthFlowService;
import com.kun.linkage.auth.service.AuthorizationExportService;
import com.kun.linkage.auth.service.ExportFileRecordService;
import com.kun.linkage.auth.utils.I18nMessageService;
import com.kun.linkage.boss.support.controller.BaseVccBossController;
import com.kun.linkage.common.base.Result;
import com.kun.linkage.common.base.page.PageResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@Tag(name = "OrgAuthorizationQueryController", description = "授权记录查询")
@RestController
@RequestMapping("/org/authorization")
public class OrgAuthorizationQueryController extends BaseVccBossController {

    @Resource
    private I18nMessageService i18nMessageService;

    @Resource
    private AuthFlowService authFlowService;

    @Resource
    private AuthorizationExportService authorizationExportService;

    @Resource
    private ExportFileRecordService exportFileRecordService;

    /**
     * 分页查询授权记录
     *
     * @param requestVO
     * @return
     */
    @Operation(description = "分页查询授权记录", summary = "分页查询授权记录")
    @PostMapping("/pageList")
    public Result<PageResult<AuthorizationInquiryPageVO>> list(@RequestBody AuthorizationInquiryReuqestVO requestVO) {
        if (StringUtils.isBlank(requestVO.getAuthorizationDateFrom())) {
            return Result.fail(KunLinkageAuthResponseCodeConstant.PARAMETER_MISSING.getCode(),
                    i18nMessageService.getMessage(KunLinkageAuthResponseCodeConstant.PARAMETER_MISSING.getCode(),
                            i18nMessageService.getMessage(AuthApplicationRequestParamNameConstant.AUTHORIZATION_DATE_FROM)));
        }
        if (StringUtils.isBlank(requestVO.getAuthorizationDateUntil())) {
            return Result.fail(KunLinkageAuthResponseCodeConstant.PARAMETER_MISSING.getCode(),
                    i18nMessageService.getMessage(KunLinkageAuthResponseCodeConstant.PARAMETER_MISSING.getCode(),
                            i18nMessageService.getMessage(AuthApplicationRequestParamNameConstant.AUTHORIZATION_DATE_TO)));
        }
        PageResult<AuthorizationInquiryPageVO> pageList = authFlowService.pageList(requestVO);
        return Result.success(pageList);
    }

    /**
     * 异步导出授权记录
     *
     * @param requestVO
     * @return
     */
    @Operation(description = "异步导出授权记录", summary = "异步导出授权记录")
    @PostMapping("/asyncExport")
    public Result<String> asyncExport(@RequestBody AuthorizationExportRequestVO requestVO) {
        if (StringUtils.isBlank(requestVO.getAuthorizationDateFrom())) {
            return Result.fail(KunLinkageAuthResponseCodeConstant.PARAMETER_MISSING.getCode(),
                    i18nMessageService.getMessage(KunLinkageAuthResponseCodeConstant.PARAMETER_MISSING.getCode(),
                            i18nMessageService.getMessage(AuthApplicationRequestParamNameConstant.AUTHORIZATION_DATE_FROM)));
        }
        if (StringUtils.isBlank(requestVO.getAuthorizationDateUntil())) {
            return Result.fail(KunLinkageAuthResponseCodeConstant.PARAMETER_MISSING.getCode(),
                    i18nMessageService.getMessage(KunLinkageAuthResponseCodeConstant.PARAMETER_MISSING.getCode(),
                            i18nMessageService.getMessage(AuthApplicationRequestParamNameConstant.AUTHORIZATION_DATE_TO)));
        }

        // 获取当前用户ID（这里需要根据实际的用户认证方式获取）
        String currentUserId = getCurrentUserId();

        return authorizationExportService.createExportTask(requestVO, currentUserId);
    }

    /**
     * 分页查询导出文件记录
     *
     * @param queryVO
     * @return
     */
    @Operation(description = "分页查询导出文件记录", summary = "分页查询导出文件记录")
    @PostMapping("/exportFiles/pageList")
    public Result<PageResult<ExportFileRecordVO>> exportFilePageList(@RequestBody ExportFileRecordQueryVO queryVO) {
        PageResult<ExportFileRecordVO> pageResult = exportFileRecordService.pageQuery(queryVO);
        return Result.success(pageResult);
    }

    /**
     * 获取文件下载URL
     *
     * @param fileRecordId
     * @return
     */
    @Operation(description = "获取文件下载URL", summary = "获取文件下载URL")
    @PostMapping("/exportFiles/downloadUrl")
    public Result<String> getDownloadUrl(@RequestBody String fileRecordId) {
        return exportFileRecordService.getDownloadUrl(fileRecordId);
    }

    /**
     * 获取当前用户ID
     * 这里需要根据实际的用户认证方式实现
     */
    private String getCurrentUserId() {
        // TODO: 根据实际的用户认证方式获取当前用户ID
        // 可能从SecurityContext、Session或者请求头中获取
        return "system"; // 临时返回系统用户
    }
}
