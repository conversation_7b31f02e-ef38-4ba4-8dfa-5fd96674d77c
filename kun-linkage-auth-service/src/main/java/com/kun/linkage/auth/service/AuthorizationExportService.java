package com.kun.linkage.auth.service;

import com.amazonaws.services.s3.model.CannedAccessControlList;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.kun.common.util.aws.AwsS3Util;
import com.kun.common.util.aws.AwzS3Properties;
import com.kun.common.util.uid.DateUtils;
import com.kun.linkage.auth.constant.ExportConstant;
import com.kun.linkage.auth.facade.constant.ExportFileStatusEnum;
import com.kun.linkage.auth.facade.constant.KunLinkageAuthResponseCodeConstant;
import com.kun.linkage.auth.facade.vo.boss.AuthorizationExportRequestVO;
import com.kun.linkage.common.base.Result;
import com.kun.linkage.common.base.exception.BusinessException;
import com.kun.linkage.common.base.utils.DateTimeUtils;
import com.kun.linkage.common.db.entity.AuthFlow;
import com.kun.linkage.common.db.entity.ExportFileRecord;
import com.kun.linkage.common.db.mapper.AuthFlowMapper;
import com.kun.linkage.common.db.mapper.ExportFileRecordMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVPrinter;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.List;

/**
 * 授权记录导出服务
 */
@Slf4j
@Service
public class AuthorizationExportService {

    @Resource
    private AuthFlowMapper authFlowMapper;

    @Resource
    private ExportFileRecordMapper exportFileRecordMapper;

    @Resource
    private AwsS3Util awsS3Util;

    @Resource
    private AwzS3Properties awzS3Properties;

    @Value("${kun.aws.s3.fileFolder:}")
    private String fileFolder;

    /**
     * 创建异步导出任务
     */
    public Result<String> createExportTask(AuthorizationExportRequestVO requestVO, String currentUserId) {
        try {
            // 1. 验证日期范围
            Result<Void> validationResult = validateDateRange(requestVO.getAuthorizationDateFrom(), 
                requestVO.getAuthorizationDateUntil());
            if (!validationResult.isSuccess()) {
                return Result.fail(validationResult.getMessage());
            }

            // 2. 生成文件名
            String fileName = generateFileName(requestVO.getMerchantNo());

            // 3. 创建文件记录
            ExportFileRecord fileRecord = new ExportFileRecord();
            fileRecord.setFileName(fileName);
            fileRecord.setFileType(ExportConstant.FILE_TYPE_CSV);
            fileRecord.setFileStatus(ExportFileStatusEnum.PROCESSING.getStatus());
            fileRecord.setMerchantNo(requestVO.getMerchantNo());
            fileRecord.setCreatedBy(currentUserId);
            fileRecord.setCreatedTime(LocalDateTime.now());
            fileRecord.setUpdatedTime(LocalDateTime.now());
            fileRecord.setExportType("AUTHORIZATION_EXPORT");

            exportFileRecordMapper.insert(fileRecord);

            // 4. 异步执行导出
            asyncExportData(fileRecord.getFileRecordId(), requestVO);

            return Result.success(fileRecord.getFileRecordId());

        } catch (Exception e) {
            log.error("创建导出任务失败", e);
            return Result.fail(KunLinkageAuthResponseCodeConstant.EXPORT_TASK_CREATE_FAILED.getCode(),
                KunLinkageAuthResponseCodeConstant.EXPORT_TASK_CREATE_FAILED.getMessage());
        }
    }

    /**
     * 验证日期范围
     */
    private Result<Void> validateDateRange(String dateFrom, String dateUntil) {
        try {
            LocalDate fromDate = LocalDate.parse(dateFrom);
            LocalDate untilDate = LocalDate.parse(dateUntil);
            
            long daysBetween = ChronoUnit.DAYS.between(fromDate, untilDate);
            
            if (daysBetween > ExportConstant.MAX_DATE_RANGE_DAYS) {
                return Result.fail(KunLinkageAuthResponseCodeConstant.DATE_RANGE_EXCEEDED.getCode(),
                    KunLinkageAuthResponseCodeConstant.DATE_RANGE_EXCEEDED.getMessage());
            }
            
            return Result.success();
        } catch (Exception e) {
            log.error("日期格式验证失败", e);
            return Result.fail("日期格式错误");
        }
    }

    /**
     * 生成文件名
     */
    private String generateFileName(String merchantNo) {
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern(ExportConstant.DATETIME_FORMAT));
        return ExportConstant.AUTHORIZATION_FILE_PREFIX + merchantNo + "_" + timestamp + ".csv";
    }

    /**
     * 异步执行数据导出
     */
    @Async("externalApiAsyncExecutor")
    public void asyncExportData(String fileRecordId, AuthorizationExportRequestVO requestVO) {
        try {
            log.info("开始异步导出授权记录数据，文件记录ID: {}", fileRecordId);

            // 1. 查询数据
            List<AuthFlow> dataList = queryExportData(requestVO);
            log.info("查询到 {} 条授权记录数据", dataList.size());

            // 2. 生成CSV文件
            File csvFile = generateCsvFile(dataList, fileRecordId);
            log.info("CSV文件生成完成，文件大小: {} bytes", csvFile.length());

            // 3. 上传到S3
            ExportFileRecord fileRecord = exportFileRecordMapper.selectById(fileRecordId);
            String s3Url = uploadToS3(csvFile, fileRecord.getFileName());
            log.info("文件上传S3成功，URL: {}", s3Url);

            // 4. 更新文件记录为成功状态
            updateFileRecordSuccess(fileRecordId, s3Url, csvFile.length());

            // 5. 清理临时文件
            if (csvFile.exists()) {
                csvFile.delete();
            }

            log.info("授权记录数据导出完成，文件记录ID: {}", fileRecordId);

        } catch (Exception e) {
            log.error("异步导出授权记录数据失败，文件记录ID: {}", fileRecordId, e);
            updateFileRecordFailed(fileRecordId, e.getMessage());
        }
    }

    /**
     * 查询导出数据
     */
    private List<AuthFlow> queryExportData(AuthorizationExportRequestVO requestVO) {
        LambdaQueryWrapper<AuthFlow> wrapper = Wrappers.lambdaQuery();
        
        // 日期范围查询
        wrapper.ge(AuthFlow::getCreateTime, DateTimeUtils.truncateToSecond(
            DateUtils.parseDate(requestVO.getAuthorizationDateFrom().trim() + " 00:00:00",
                DateUtils.DATETIME_PATTERN)));
        wrapper.le(AuthFlow::getCreateTime, DateTimeUtils.truncateToSecond(
            DateUtils.parseDate(requestVO.getAuthorizationDateUntil().trim() + " 23:59:59",
                DateUtils.DATETIME_PATTERN)));

        // 其他查询条件
        if (requestVO.getTransAmountFrom() != null) {
            wrapper.ge(AuthFlow::getTransAmount, requestVO.getTransAmountFrom());
        }
        if (requestVO.getTransAmountTo() != null) {
            wrapper.le(AuthFlow::getTransAmount, requestVO.getTransAmountTo());
        }
        if (StringUtils.isNotBlank(requestVO.getMerchantNo())) {
            wrapper.eq(AuthFlow::getMerchantNo, requestVO.getMerchantNo());
        }
        if (StringUtils.isNotBlank(requestVO.getAcquirerMerchantNo())) {
            wrapper.eq(AuthFlow::getCardAcceptorId, requestVO.getAcquirerMerchantNo());
        }
        if (StringUtils.isNotBlank(requestVO.getAuthTransId())) {
            wrapper.eq(AuthFlow::getId, requestVO.getAuthTransId());
        }
        if (StringUtils.isNotBlank(requestVO.getAuthTransType())) {
            wrapper.eq(AuthFlow::getTransType, requestVO.getAuthTransType());
        }
        if (StringUtils.isNotBlank(requestVO.getOriginalAuthTransId())) {
            wrapper.eq(AuthFlow::getOriginalId, requestVO.getOriginalAuthTransId());
        }
        if (StringUtils.isNotBlank(requestVO.getAcquirerMerchantMcc())) {
            wrapper.eq(AuthFlow::getMcc, requestVO.getAcquirerMerchantMcc());
        }
        if (StringUtils.isNotBlank(requestVO.getAcquireReferenceNo())) {
            wrapper.eq(AuthFlow::getAcquireReferenceNo, requestVO.getAcquireReferenceNo());
        }
        if (StringUtils.isNotBlank(requestVO.getGatewayCardId())) {
            wrapper.eq(AuthFlow::getGatewayCardId, requestVO.getGatewayCardId());
        }
        if (StringUtils.isNotBlank(requestVO.getAuthStatus())) {
            wrapper.eq(AuthFlow::getStatus, requestVO.getAuthStatus());
        }
        if (StringUtils.isNotBlank(requestVO.getResponseCode())) {
            wrapper.eq(AuthFlow::getResponseCode, requestVO.getResponseCode());
        }
        if (StringUtils.isNotBlank(requestVO.getSystemsTraceAuditNumber())) {
            wrapper.eq(AuthFlow::getSystemsTraceAuditNumber, requestVO.getSystemsTraceAuditNumber());
        }
        if (StringUtils.isNotBlank(requestVO.getApproveCode())) {
            wrapper.eq(AuthFlow::getApproveCode, requestVO.getApproveCode());
        }
        if (StringUtils.isNotBlank(requestVO.getProcessor())) {
            wrapper.eq(AuthFlow::getProcessor, requestVO.getProcessor());
        }
        if (StringUtils.isNotBlank(requestVO.getCardNoSuffix())) {
            wrapper.eq(AuthFlow::getMaskedCardNo, requestVO.getCardNoSuffix());
        }

        wrapper.orderByDesc(AuthFlow::getCreateTime);
        
        return authFlowMapper.selectList(wrapper);
    }

    /**
     * 生成CSV文件
     */
    private File generateCsvFile(List<AuthFlow> dataList, String fileRecordId) throws IOException {
        File tempFile = File.createTempFile("auth_export_" + fileRecordId, ".csv");
        
        try (FileWriter writer = new FileWriter(tempFile);
             CSVPrinter csvPrinter = new CSVPrinter(writer, CSVFormat.DEFAULT.withHeader(ExportConstant.CSV_HEADERS))) {
            
            for (AuthFlow authFlow : dataList) {
                csvPrinter.printRecord(
                    DateUtils.formatDate(authFlow.getCreateTime(), DateUtils.DATETIME_PATTERN), // 授权日期时间
                    authFlow.getId(),                                                           // 系统授权号
                    authFlow.getMerchantNo(),                                                   // 商户号
                    authFlow.getGatewayCardId(),                                               // Card ID
                    authFlow.getTransType(),                                                   // 授权类型
                    authFlow.getTransCurrency(),                                               // 交易币种
                    formatAmount(authFlow.getTransAmount()),                                   // 交易金额
                    authFlow.getCardholderBillingCurrency(),                                   // 持卡人币种
                    formatAmount(authFlow.getCardholderBillingAmount()),                       // 持卡人金额
                    authFlow.getStatus(),                                                      // 授权状态
                    authFlow.getAcquireReferenceNo(),                                          // 参考号
                    authFlow.getApproveCode(),                                                 // 授权码
                    authFlow.getOriginalId(),                                                  // 原系统授权号
                    authFlow.getCardAcceptorName(),                                            // 收单商户名称
                    authFlow.getMcc(),                                                         // 收单商户MCC
                    authFlow.getClearFlag()                                                    // 清算状态
                );
            }
        }
        
        return tempFile;
    }

    /**
     * 格式化金额
     */
    private String formatAmount(BigDecimal amount) {
        return amount != null ? amount.toPlainString() : "";
    }

    /**
     * 上传文件到S3
     */
    private String uploadToS3(File file, String fileName) {
        return awsS3Util.uploadChunkedFile(
            fileName,
            file,
            awzS3Properties.getBucket(),
            fileFolder + ExportConstant.S3_FOLDER_PATH,
            CannedAccessControlList.PublicReadWrite
        );
    }

    /**
     * 更新文件记录为成功状态
     */
    private void updateFileRecordSuccess(String fileRecordId, String s3Url, long fileSize) {
        LambdaUpdateWrapper<ExportFileRecord> updateWrapper = Wrappers.lambdaUpdate();
        updateWrapper.eq(ExportFileRecord::getFileRecordId, fileRecordId)
            .set(ExportFileRecord::getFileStatus, ExportFileStatusEnum.SUCCESS.getStatus())
            .set(ExportFileRecord::getS3Url, s3Url)
            .set(ExportFileRecord::getFileSize, fileSize)
            .set(ExportFileRecord::getUpdatedTime, LocalDateTime.now());
        
        exportFileRecordMapper.update(null, updateWrapper);
    }

    /**
     * 更新文件记录为失败状态
     */
    private void updateFileRecordFailed(String fileRecordId, String errorMessage) {
        LambdaUpdateWrapper<ExportFileRecord> updateWrapper = Wrappers.lambdaUpdate();
        updateWrapper.eq(ExportFileRecord::getFileRecordId, fileRecordId)
            .set(ExportFileRecord::getFileStatus, ExportFileStatusEnum.FAILED.getStatus())
            .set(ExportFileRecord::getErrorMessage, errorMessage)
            .set(ExportFileRecord::getUpdatedTime, LocalDateTime.now());
        
        exportFileRecordMapper.update(null, updateWrapper);
    }
}
