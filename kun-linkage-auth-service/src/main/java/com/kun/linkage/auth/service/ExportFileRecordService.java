package com.kun.linkage.auth.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.kun.linkage.auth.facade.constant.KunLinkageAuthResponseCodeConstant;
import com.kun.linkage.auth.facade.vo.boss.ExportFileRecordQueryVO;
import com.kun.linkage.auth.facade.vo.boss.ExportFileRecordVO;
import com.kun.linkage.common.base.Result;
import com.kun.linkage.common.base.page.PageHelperUtil;
import com.kun.linkage.common.base.page.PageResult;
import com.kun.linkage.common.db.entity.ExportFileRecord;
import com.kun.linkage.common.db.mapper.ExportFileRecordMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 导出文件记录管理服务
 */
@Slf4j
@Service
public class ExportFileRecordService {

    @Resource
    private ExportFileRecordMapper exportFileRecordMapper;

    /**
     * 分页查询导出文件记录
     */
    public PageResult<ExportFileRecordVO> pageQuery(ExportFileRecordQueryVO queryVO) {
        LambdaQueryWrapper<ExportFileRecord> wrapper = Wrappers.lambdaQuery();

        // 文件名模糊查询
        if (StringUtils.isNotBlank(queryVO.getFileName())) {
            wrapper.like(ExportFileRecord::getFileName, queryVO.getFileName());
        }

        // 文件状态
        if (StringUtils.isNotBlank(queryVO.getFileStatus())) {
            wrapper.eq(ExportFileRecord::getFileStatus, queryVO.getFileStatus());
        }

        // 商户号
        if (StringUtils.isNotBlank(queryVO.getMerchantNo())) {
            wrapper.eq(ExportFileRecord::getMerchantNo, queryVO.getMerchantNo());
        }

        // 导出类型
        if (StringUtils.isNotBlank(queryVO.getExportType())) {
            wrapper.eq(ExportFileRecord::getExportType, queryVO.getExportType());
        }

        // 创建日期范围
        if (StringUtils.isNotBlank(queryVO.getCreatedDateFrom())) {
            LocalDateTime fromDateTime = LocalDate.parse(queryVO.getCreatedDateFrom()).atStartOfDay();
            wrapper.ge(ExportFileRecord::getCreatedTime, fromDateTime);
        }

        if (StringUtils.isNotBlank(queryVO.getCreatedDateUntil())) {
            LocalDateTime untilDateTime = LocalDate.parse(queryVO.getCreatedDateUntil()).atTime(LocalTime.MAX);
            wrapper.le(ExportFileRecord::getCreatedTime, untilDateTime);
        }

        // 按创建时间倒序
        wrapper.orderByDesc(ExportFileRecord::getCreatedTime);

        PageResult<ExportFileRecord> pageResult = PageHelperUtil.getPage(queryVO, 
            () -> exportFileRecordMapper.selectList(wrapper));

        return convertToPageVO(pageResult);
    }

    /**
     * 根据文件记录ID获取文件信息
     */
    public Result<ExportFileRecordVO> getFileRecord(String fileRecordId) {
        ExportFileRecord fileRecord = exportFileRecordMapper.selectById(fileRecordId);
        
        if (fileRecord == null) {
            return Result.fail(KunLinkageAuthResponseCodeConstant.FILE_RECORD_NOT_FOUND.getCode(),
                KunLinkageAuthResponseCodeConstant.FILE_RECORD_NOT_FOUND.getMessage());
        }

        ExportFileRecordVO vo = convertToVO(fileRecord);
        return Result.success(vo);
    }

    /**
     * 获取文件下载URL
     */
    public Result<String> getDownloadUrl(String fileRecordId) {
        ExportFileRecord fileRecord = exportFileRecordMapper.selectById(fileRecordId);
        
        if (fileRecord == null) {
            return Result.fail(KunLinkageAuthResponseCodeConstant.FILE_RECORD_NOT_FOUND.getCode(),
                KunLinkageAuthResponseCodeConstant.FILE_RECORD_NOT_FOUND.getMessage());
        }

        if (StringUtils.isBlank(fileRecord.getS3Url())) {
            return Result.fail("文件尚未生成或生成失败");
        }

        return Result.success(fileRecord.getS3Url());
    }

    /**
     * 转换分页结果
     */
    private PageResult<ExportFileRecordVO> convertToPageVO(PageResult<ExportFileRecord> pageResult) {
        List<ExportFileRecordVO> voList = pageResult.getData().stream()
            .map(this::convertToVO)
            .collect(Collectors.toList());

        return new PageResult<>(voList, pageResult.getPageNum(), pageResult.getPageSize(), 
            pageResult.getTotal(), pageResult.getExtraInfo());
    }

    /**
     * 转换实体为VO
     */
    private ExportFileRecordVO convertToVO(ExportFileRecord fileRecord) {
        ExportFileRecordVO vo = new ExportFileRecordVO();
        vo.setFileRecordId(fileRecord.getFileRecordId());
        vo.setFileName(fileRecord.getFileName());
        vo.setFileType(fileRecord.getFileType());
        vo.setFileStatus(fileRecord.getFileStatus());
        vo.setS3Url(fileRecord.getS3Url());
        vo.setFileSize(fileRecord.getFileSize());
        vo.setMerchantNo(fileRecord.getMerchantNo());
        vo.setCreatedBy(fileRecord.getCreatedBy());
        vo.setCreatedTime(fileRecord.getCreatedTime());
        vo.setUpdatedTime(fileRecord.getUpdatedTime());
        vo.setErrorMessage(fileRecord.getErrorMessage());
        vo.setExportType(fileRecord.getExportType());
        return vo;
    }
}
